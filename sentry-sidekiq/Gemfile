# frozen_string_literal: true

source "https://rubygems.org"
git_source(:github) { |name| "https://github.com/#{name}.git" }

eval_gemfile "../Gemfile"

# Specify your gem's dependencies in sentry-ruby.gemspec
gemspec

gem "sentry-ruby", path: "../sentry-ruby"
gem "sentry-rails", path: "../sentry-rails"

# https://github.com/flavorjones/loofah/pull/267
# loofah changed the required ruby version in a patch so we need to explicitly pin it
gem "loofah", "2.20.0" if RUBY_VERSION.to_f < 2.5

if ENV["SIDEKIQ_MAIN"]
  gem "sidekiq", github: "sidekiq/sidekiq", branch: "main"
  sidekiq_version = "main"
else
  sidekiq_version = ENV["SIDEKIQ_VERSION"]
  sidekiq_version = "7.0" if sidekiq_version.nil?
  sidekiq_version = Gem::Version.new(sidekiq_version)

  gem "sidekiq", "~> #{sidekiq_version}"
end

if sidekiq_version == "main" || RUBY_VERSION.to_f >= 2.7 && sidekiq_version >= Gem::Version.new("6.0")
  gem "sidekiq-cron"

  if sidekiq_version == "main" || sidekiq_version >= Gem::Version.new("8.0")
    gem "sidekiq-scheduler", "~> 6.0.0.beta"
  else
    gem "sidekiq-scheduler", "~> 5.0.0"
  end
end

gem "rails", "> 5.0.0"

gem "timecop"

gem "vernier", platforms: :ruby if RUBY_VERSION >= "3.2.1"
