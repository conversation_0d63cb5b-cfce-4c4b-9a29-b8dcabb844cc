# frozen_string_literal: true

require "rake/clean"
CLOBBER.include "pkg"

require "bundler/gem_helper"
Bundler::GemHelper.install_tasks(name: "sentry-ruby")

require "rspec/core/rake_task"

ISOLATED_SPECS = "spec/isolated/**/*_spec.rb"

RSpec::Core::RakeTask.new(:spec).tap do |task|
  task.exclude_pattern = ISOLATED_SPECS
end

RSpec::Core::RakeTask.new(:isolated_specs).tap do |task|
  task.pattern = ISOLATED_SPECS
end

task default: [:spec, :isolated_specs]
