name: sentry-rails Test

on:
  workflow_dispatch:
  workflow_call:
    inputs:
      versions:
        required: true
        type: string
# Cancel in progress workflows on pull_requests.
# https://docs.github.com/en/actions/using-jobs/using-concurrency#example-using-a-fallback-value
concurrency:
  group: sentry-rails-test-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true
jobs:
  test:
    defaults:
      run:
        working-directory: sentry-rails
    name: Ruby ${{ matrix.ruby_version }} & Rails ${{ matrix.rails_version }}, options - ${{ toJson(matrix.options) }}
    runs-on: ubuntu-latest
    timeout-minutes: 10
    env:
      RUBYOPT: ${{ matrix.options.rubyopt }}
      BUNDLE_GEMFILE: ${{ github.workspace }}/sentry-rails/Gemfile
      BUNDLE_WITHOUT: rubocop
      RAILS_VERSION: ${{ matrix.rails_version }}
    strategy:
      fail-fast: false
      matrix:
        ruby_version: ${{ from<PERSON>son(inputs.versions) }}
        rails_version: [6.1.0, 7.0.0, 7.1.0]
        include:
          - ruby_version: "2.4"
            rails_version: 5.0.0
          - ruby_version: "2.4"
            rails_version: 5.1.0
          - ruby_version: "2.4"
            rails_version: 5.2.0
          - ruby_version: "2.5"
            rails_version: 5.0.0
          - ruby_version: "2.5"
            rails_version: 5.1.0
          - ruby_version: "2.5"
            rails_version: 5.2.0
          - ruby_version: "2.5"
            rails_version: 6.0.0
          - ruby_version: "2.5"
            rails_version: 6.1.0
          - ruby_version: "2.6"
            rails_version: 5.0.0
          - ruby_version: "2.6"
            rails_version: 5.1.0
          - ruby_version: "2.6"
            rails_version: 5.2.0
          - ruby_version: "2.6"
            rails_version: 6.0.0
          - ruby_version: "2.6"
            rails_version: 6.1.0
          - ruby_version: "2.7"
            rails_version: 5.0.0
          - ruby_version: "2.7"
            rails_version: 5.1.0
          - ruby_version: "2.7"
            rails_version: 5.2.0
          - ruby_version: "2.7"
            rails_version: 6.0.0
          - ruby_version: "2.7"
            rails_version: 6.1.0
          - ruby_version: "3.1"
            rails_version: 7.2.0
          - ruby_version: "3.2"
            rails_version: 7.2.0
          - ruby_version: "3.3"
            rails_version: 7.2.0
          - ruby_version: "3.4"
            rails_version: 7.2.0
          - ruby_version: "3.2"
            rails_version: "8.0.0"
          - ruby_version: "3.3"
            rails_version: "8.0.0"
          - ruby_version: "3.4"
            rails_version: "8.0.0"
          - ruby_version: "jruby"
            rails_version: 6.1.0
          - ruby_version: "3.2"
            rails_version: 7.1.0
            options:
              rubyopt: "--enable-frozen-string-literal --debug=frozen-string-literal"
          - ruby_version: "3.2"
            rails_version: 7.1.0
        exclude:
          - ruby_version: head
          - ruby_version: 'jruby'
          - ruby_version: jruby-head
          - ruby_version: "3.4"
            rails_version: "6.1.0"
          - ruby_version: "3.4"
            rails_version: "7.0.0"

    steps:
      - uses: actions/checkout@v4
      - name: Install sqlite and ImageMagick
        run: |
          # See https://github.community/t5/GitHub-Actions/ubuntu-latest-Apt-repository-list-issues/td-p/41122/page/2
          for apt_file in `grep -lr microsoft /etc/apt/sources.list.d/`; do sudo rm $apt_file; done
          sudo apt-get update
          sudo apt-get install libsqlite3-dev imagemagick
      - name: Set up Ruby ${{ matrix.ruby_version }}
        uses: ruby/setup-ruby@v1
        with:
          ruby-version: ${{ matrix.ruby_version }}
          bundler-cache: true

      - name: Build with Rails ${{ matrix.rails_version }}
        run: bundle exec rake

      - name: Upload Coverage
        uses: codecov/codecov-action@v5
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
