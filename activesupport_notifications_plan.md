# ActiveSupport::Notifications Integration for Sentry Structured Logging

## Analysis Summary

### Current State
The Sentry Ruby SDK now supports Structured Logging via `Sentry.logger`, which implements the [Sentry Logs Protocol](https://develop.sentry.dev/sdk/telemetry/logs/). The structured logger provides methods for logging at different severity levels with structured data support.

**Key Components:**
- `Sentry::StructuredLogger` - Main logging interface
- `Sentry.capture_log` - Underlying log capture method
- `Sentry::LogEvent` - Log event representation
- `Sentry::LogEventBuffer` - Buffering mechanism
- Configuration via `config.enable_logs = true`

### Existing ActiveSupport::Notifications Usage
The codebase already has extensive ActiveSupport::Notifications integration that we must avoid duplicating:

#### 1. **Rails Tracing Integration** - Performance spans for transactions
**Currently Traced Events:**
- **ActiveRecordSubscriber**: `sql.active_record` (excludes SCHEMA, TRANSACTION)
- **ActionViewSubscriber**: `render_template.action_view`
- **ActiveSupportSubscriber**: Cache operations (`cache_read/write/delete/prune.active_support`)
- **ActiveStorageSubscriber**: 12 file operation events (`service_*.active_storage`, `preview.active_storage`, `analyze.active_storage`)
- **ActionControllerSubscriber**: `process_action.action_controller` (DEPRECATED)

**Key Characteristics:**
- Only creates spans when a transaction is active (`Tracing.get_current_transaction`)
- Extracts detailed performance data (duration, database info, cache keys, etc.)
- Respects `send_default_pii` configuration for sensitive data

#### 2. **Breadcrumbs Integration** - Contextual event logging
**Currently Captured Events:**
- **47 different event types** via `ACTIVE_SUPPORT_LOGGER_SUBSCRIPTION_ITEMS_DEFAULT`
- Includes ALL Rails core events: ActionController, ActionView, ActiveRecord, ActiveJob, ActionCable, ActiveStorage, ActionMailer
- Uses wildcard subscription (`/.*/) but filters by configured event types
- Captures specific payload attributes per event type (e.g., controller/action for process_action.action_controller)

#### 3. **Active Job Integration** - Background job monitoring
- **SentryReporter**: Creates transactions for job execution with error handling
- **Event Handlers**: Subscribes to `enqueue_retry.active_job` for retry failures
- **Breadcrumbs**: Captures job lifecycle events (enqueue, perform, retry_stopped, discard)

#### 4. **Established Patterns:**
- Abstract subscriber base class (`AbstractSubscriber`) with Rails version compatibility
- Configuration-driven subscriber registration via `tracing_subscribers`
- Event filtering and data cleanup utilities
- Integration with Rails configuration system

### ⚠️ CRITICAL: Avoiding Duplication
**The following events are ALREADY CAPTURED and should NOT be duplicated:**

#### Already Traced (Performance Spans):
- `sql.active_record` - Database queries (with detailed DB info)
- `render_template.action_view` - Template rendering
- `cache_*.active_support` - Cache operations (read/write/delete/prune)
- All `*.active_storage` events - File operations (12 events)

#### Already in Breadcrumbs (Contextual Logging):
- `process_action.action_controller` - Controller actions
- `start_processing.action_controller` - Request start
- All Active Job events (`enqueue.active_job`, `perform.active_job`, etc.)
- All ActionMailer events (`deliver.action_mailer`, `process.action_mailer`)
- All ActionCable events (`perform_action.action_cable`, `broadcast.action_cable`)
- **47 total event types** - See `ACTIVE_SUPPORT_LOGGER_SUBSCRIPTION_ITEMS_DEFAULT`

### Actual Integration Opportunities
**Events NOT currently captured that could benefit from structured logging:**

- **Third-party Gem Events:**
  - HTTP client library events (Faraday, Net::HTTP, HTTParty)
  - Redis operations (redis-rb, sidekiq-redis)
  - Elasticsearch queries (elasticsearch-ruby)
  - GraphQL operations (graphql-ruby)
  - Background job libraries outside Rails (Sidekiq, Resque, DelayedJob custom events)

- **Custom Application Events:**
  - Business logic events published by applications
  - Custom instrumentation from gems not covered above
  - Performance-critical operations that need structured logging

- **Enhanced Context for Existing Events:**
  - Correlation between tracing spans and structured logs
  - Additional metadata not captured in current integrations
  - Error context for failed operations

## Revised Implementation Plan

### Phase 1: Core Infrastructure
**Goal:** Create foundational components for NON-DUPLICATED ActiveSupport::Notifications logging

#### Task 1.1: Create ActiveSupport::Notifications Subscriber Base
- Create `Sentry::Notifications::AbstractSubscriber` base class (reuse existing patterns)
- Implement subscription management (subscribe/unsubscribe)
- Add event filtering to EXCLUDE already-traced events
- Handle Rails version compatibility (Rails 5 vs 6+)
- **CRITICAL**: Ensure no overlap with existing tracing/breadcrumb subscribers

#### Task 1.2: Create Configuration System
- Add `config.notifications` configuration namespace
- Implement subscriber registration system
- Add enable/disable toggles for different event categories
- Support custom event patterns and filtering
- **CRITICAL**: Default to EXCLUDE all events already captured by tracing/breadcrumbs

#### Task 1.3: Create Event Processor
- Implement event data sanitization and formatting
- Add payload filtering for sensitive data
- Create structured log message formatting
- Handle event timing and metadata extraction
- **CRITICAL**: Coordinate with existing span/breadcrumb data to avoid duplication

### Phase 2: Non-Duplicated Subscribers
**Goal:** Implement subscribers for events NOT already captured

#### Task 2.1: Third-party HTTP Client Subscriber
- Subscribe to HTTP request/response events from Faraday, Net::HTTP, HTTParty
- Log request timing and status codes
- Handle request/response body logging (with PII considerations)
- Support multiple HTTP client libraries

#### Task 2.2: Database/Cache Client Subscriber
- Subscribe to Redis operations (redis-rb events)
- Subscribe to Elasticsearch queries
- Subscribe to other database client events NOT covered by ActiveRecord
- Handle connection pooling and performance metrics

#### Task 2.3: Custom Application Events Subscriber
- Provide framework for custom business logic events
- Support GraphQL operations and custom instrumentation
- Handle application-specific performance events
- Enable correlation with existing traces/breadcrumbs

### Phase 3: Advanced Features
**Goal:** Add advanced configuration and customization capabilities

#### Task 3.1: Custom Event Mapping
- Allow custom log level mapping per event type
- Support custom message templates
- Enable custom attribute extraction from payloads
- Add event correlation with traces/spans

#### Task 3.2: Performance Optimization
- Implement efficient event filtering
- Add sampling support for high-volume events
- Optimize memory usage for event buffering
- Add async processing options

#### Task 3.3: Integration Testing
- Create comprehensive test suite
- Add integration tests with real Rails applications
- Test with various Ruby gem combinations
- Performance benchmarking

### Phase 4: Documentation and Examples
**Goal:** Provide comprehensive documentation and usage examples

#### Task 4.1: API Documentation
- Document configuration options
- Provide API reference for custom subscribers
- Create migration guide from existing logging solutions

#### Task 4.2: Usage Examples
- Create example configurations for common scenarios
- Provide custom subscriber implementation examples
- Document best practices and performance considerations

## Technical Design Considerations

### Revised Configuration Structure
```ruby
Sentry.init do |config|
  config.enable_logs = true
  config.notifications.enabled = true
  config.notifications.subscribers = [
    :http_client,      # HTTP request events (Faraday, Net::HTTP)
    :redis_client,     # Redis operations
    :elasticsearch,    # Elasticsearch queries
    MyCustomSubscriber # Custom subscriber class
  ]
  # CRITICAL: Exclude events already captured by tracing/breadcrumbs
  config.notifications.excluded_events = [
    'sql.active_record',           # Already traced
    'render_template.action_view', # Already traced
    'cache_*.active_support',      # Already traced
    'process_action.action_controller', # Already in breadcrumbs
    'perform.active_job'           # Already in breadcrumbs + Active Job integration
  ]
  config.notifications.event_filters = {
    'http_request.faraday' => { exclude_body: true },
    'redis.command' => { max_length: 1000 }
  }
end
```

### Revised Subscriber Interface
```ruby
class Sentry::Notifications::HttpClientSubscriber < Sentry::Notifications::AbstractSubscriber
  EVENT_PATTERNS = [
    'request.faraday',
    'request.net_http',
    'request.httparty'
  ].freeze

  def self.subscribe!
    subscribe_to_events(EVENT_PATTERNS) do |event_name, event|
      # Only log if NOT already captured by tracing
      next if already_traced?(event_name, event)

      log_event(event_name, event)
    end
  end

  private

  def self.log_event(event_name, event)
    Sentry.logger.info(
      "HTTP #{event_name}",
      duration: event.duration,
      status_code: event.payload[:status],
      url: sanitize_url(event.payload[:url]),
      method: event.payload[:method]
    )
  end

  def self.already_traced?(event_name, event)
    # Check if this request is already being traced as a span
    Sentry.get_current_scope.get_transaction&.get_span_by_description(event.payload[:url])
  end
end
```

### Critical Integration Points
- **AVOID DUPLICATION**: Must not subscribe to events already handled by tracing/breadcrumbs
- **Reuse existing patterns**: Leverage Rails configuration and AbstractSubscriber patterns
- **Coordinate with spans**: Detect when events are already being traced to avoid double-logging
- **Respect existing configuration**: Honor `send_default_pii`, `excluded_exceptions`, etc.
- **Maintain performance**: Ensure minimal overhead for events already being processed

### Success Metrics
- Zero-configuration setup for common Rails events
- Configurable event filtering and sampling
- Performance impact < 5% for typical applications
- Comprehensive test coverage (>95%)
- Clear documentation and examples

## Implementation Task List

A detailed task breakdown has been created with the following structure:

**Phase 1: Core Infrastructure**
- Create ActiveSupport::Notifications Subscriber Base
- Create Configuration System
- Create Event Processor

**Phase 2: Non-Duplicated Subscribers**
- Third-party HTTP Client Subscriber
- Database/Cache Client Subscriber
- Custom Application Events Subscriber

**Phase 3: Advanced Features**
- Custom Event Mapping
- Performance Optimization
- Integration Testing

**Phase 4: Documentation and Examples**
- API Documentation
- Usage Examples

Each phase builds upon the previous one, ensuring a solid foundation before adding more advanced features. The implementation should start with Phase 1 to establish the core infrastructure that all other components will depend on.

## Key Implementation Notes

### Compatibility Considerations
- Must work with Rails 5.0+ (different ActiveSupport::Notifications::Event APIs)
- Should integrate seamlessly with existing Sentry Rails integration
- Must not conflict with existing breadcrumb or tracing subscribers
- Should handle cases where ActiveSupport::Notifications is not available

### Security and Privacy
- Implement robust payload filtering to prevent PII leakage
- Provide configuration options to exclude sensitive event types
- Follow existing Sentry patterns for `send_default_pii` configuration
- Ensure SQL queries and request bodies can be sanitized

### Performance Considerations
- Use efficient event pattern matching to minimize overhead
- Implement sampling to handle high-volume events
- Consider async processing for non-critical events
- Reuse existing event filtering and cleanup utilities from Rails integration

## Summary of Key Findings

### What's Already Covered (DO NOT DUPLICATE)
1. **Performance Tracing**: 4 subscribers already create spans for database, cache, file, and view operations
2. **Breadcrumbs**: 47 event types already captured for contextual logging including all Rails core events
3. **Active Job**: Complete integration with transactions, error handling, and breadcrumbs
4. **Configuration**: Robust system already exists for managing subscribers and event filtering

### What's Missing (IMPLEMENTATION TARGETS)
1. **Third-party HTTP clients**: Faraday, Net::HTTP, HTTParty events
2. **Non-ActiveRecord databases**: Redis, Elasticsearch, MongoDB operations
3. **Custom application events**: Business logic, GraphQL, custom instrumentation
4. **Enhanced correlation**: Linking structured logs with existing traces/breadcrumbs

### Implementation Strategy
- **Phase 1**: Build infrastructure that explicitly excludes existing events
- **Phase 2**: Focus on third-party and custom events only
- **Phase 3**: Add correlation and advanced features
- **Phase 4**: Document the complementary nature with existing integrations

This approach ensures the new structured logging feature enhances rather than duplicates the existing comprehensive ActiveSupport::Notifications integration.
