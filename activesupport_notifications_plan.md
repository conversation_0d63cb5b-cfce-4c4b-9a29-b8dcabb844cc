# ActiveSupport::Notifications Integration for Sentry Structured Logging

## Analysis Summary

### Current State
The Sentry Ruby SDK now supports Structured Logging via `Sentry.logger`, which implements the [Sentry Logs Protocol](https://develop.sentry.dev/sdk/telemetry/logs/). The structured logger provides methods for logging at different severity levels with structured data support.

**Key Components:**
- `Sentry::StructuredLogger` - Main logging interface
- `Sentry.capture_log` - Underlying log capture method
- `Sentry::LogEvent` - Log event representation
- `Sentry::LogEventBuffer` - Buffering mechanism
- Configuration via `config.enable_logs = true`

### Existing ActiveSupport::Notifications Usage
The codebase already has extensive ActiveSupport::Notifications integration patterns:

1. **Rails Tracing Integration** - Multiple subscribers for performance tracing:
   - `ActiveSupportSubscriber` - Cache operations
   - `ActiveRecordSubscriber` - Database queries
   - `ActiveStorageSubscriber` - File operations
   - `ActionViewSubscriber` - Template rendering

2. **Breadcrumbs Integration** - Two logger implementations:
   - `ActiveSupportLogger` - Standard breadcrumb logging
   - `MonotonicActiveSupportLogger` - Monotonic timestamp support

3. **Established Patterns:**
   - Abstract subscriber base class (`AbstractSubscriber`)
   - Configuration-driven subscriber registration
   - Event filtering and data cleanup
   - Integration with Rails configuration system

### Integration Opportunities
Common ActiveSupport::Notifications events from various gems that could benefit from structured logging:

- **Rails Core Events:**
  - `process_action.action_controller` - Controller actions
  - `render_template.action_view` - Template rendering
  - `sql.active_record` - Database queries
  - `cache_*.active_support` - Cache operations

- **Background Job Events:**
  - `perform_start.active_job` / `perform.active_job`
  - `enqueue.active_job` / `enqueue_at.active_job`
  - `retry_stopped.active_job` / `discard.active_job`

- **Third-party Gem Events:**
  - Sidekiq, Resque, DelayedJob events
  - HTTP client library events (Faraday, Net::HTTP)
  - Redis operations
  - Elasticsearch queries

## Implementation Plan

### Phase 1: Core Infrastructure
**Goal:** Create the foundational components for generic ActiveSupport::Notifications logging

#### Task 1.1: Create ActiveSupport::Notifications Subscriber Base
- Create `Sentry::Notifications::AbstractSubscriber` base class
- Implement subscription management (subscribe/unsubscribe)
- Add event filtering capabilities
- Handle Rails version compatibility (Rails 5 vs 6+)

#### Task 1.2: Create Configuration System
- Add `config.notifications` configuration namespace
- Implement subscriber registration system
- Add enable/disable toggles for different event categories
- Support custom event patterns and filtering

#### Task 1.3: Create Event Processor
- Implement event data sanitization and formatting
- Add payload filtering for sensitive data
- Create structured log message formatting
- Handle event timing and metadata extraction

### Phase 2: Built-in Subscribers
**Goal:** Implement subscribers for common Rails and Ruby gem events

#### Task 2.1: Rails Core Events Subscriber
- Subscribe to controller action events
- Subscribe to view rendering events
- Subscribe to Active Record query events
- Subscribe to cache operation events

#### Task 2.2: Background Job Events Subscriber
- Subscribe to Active Job lifecycle events
- Handle job performance metrics
- Log job failures and retries
- Support custom job metadata

#### Task 2.3: HTTP Client Events Subscriber
- Subscribe to HTTP request/response events
- Log request timing and status codes
- Handle request/response body logging (with PII considerations)
- Support multiple HTTP client libraries

### Phase 3: Advanced Features
**Goal:** Add advanced configuration and customization capabilities

#### Task 3.1: Custom Event Mapping
- Allow custom log level mapping per event type
- Support custom message templates
- Enable custom attribute extraction from payloads
- Add event correlation with traces/spans

#### Task 3.2: Performance Optimization
- Implement efficient event filtering
- Add sampling support for high-volume events
- Optimize memory usage for event buffering
- Add async processing options

#### Task 3.3: Integration Testing
- Create comprehensive test suite
- Add integration tests with real Rails applications
- Test with various Ruby gem combinations
- Performance benchmarking

### Phase 4: Documentation and Examples
**Goal:** Provide comprehensive documentation and usage examples

#### Task 4.1: API Documentation
- Document configuration options
- Provide API reference for custom subscribers
- Create migration guide from existing logging solutions

#### Task 4.2: Usage Examples
- Create example configurations for common scenarios
- Provide custom subscriber implementation examples
- Document best practices and performance considerations

## Technical Design Considerations

### Configuration Structure
```ruby
Sentry.init do |config|
  config.enable_logs = true
  config.notifications.enabled = true
  config.notifications.subscribers = [
    :rails_core,      # Built-in Rails events
    :active_job,      # Background job events
    :http_client,     # HTTP request events
    MyCustomSubscriber # Custom subscriber class
  ]
  config.notifications.event_filters = {
    'sql.active_record' => { max_length: 1000 },
    'process_action.action_controller' => { exclude_params: true }
  }
end
```

### Subscriber Interface
```ruby
class Sentry::Notifications::RailsCoreSubscriber < Sentry::Notifications::AbstractSubscriber
  EVENT_PATTERNS = [
    'process_action.action_controller',
    'render_template.action_view'
  ].freeze

  def self.subscribe!
    subscribe_to_events(EVENT_PATTERNS) do |event_name, event|
      log_event(event_name, event)
    end
  end

  private

  def self.log_event(event_name, event)
    Sentry.logger.info(
      "Rails #{event_name}",
      duration: event.duration,
      **extract_attributes(event.payload)
    )
  end
end
```

### Integration Points
- Leverage existing Rails configuration patterns
- Reuse event filtering and cleanup utilities
- Integrate with existing breadcrumb and tracing systems
- Maintain backward compatibility with current logging

### Success Metrics
- Zero-configuration setup for common Rails events
- Configurable event filtering and sampling
- Performance impact < 5% for typical applications
- Comprehensive test coverage (>95%)
- Clear documentation and examples

## Implementation Task List

A detailed task breakdown has been created with the following structure:

**Phase 1: Core Infrastructure**
- Create ActiveSupport::Notifications Subscriber Base
- Create Configuration System
- Create Event Processor

**Phase 2: Built-in Subscribers**
- Rails Core Events Subscriber
- Background Job Events Subscriber
- HTTP Client Events Subscriber

**Phase 3: Advanced Features**
- Custom Event Mapping
- Performance Optimization
- Integration Testing

**Phase 4: Documentation and Examples**
- API Documentation
- Usage Examples

Each phase builds upon the previous one, ensuring a solid foundation before adding more advanced features. The implementation should start with Phase 1 to establish the core infrastructure that all other components will depend on.

## Key Implementation Notes

### Compatibility Considerations
- Must work with Rails 5.0+ (different ActiveSupport::Notifications::Event APIs)
- Should integrate seamlessly with existing Sentry Rails integration
- Must not conflict with existing breadcrumb or tracing subscribers
- Should handle cases where ActiveSupport::Notifications is not available

### Security and Privacy
- Implement robust payload filtering to prevent PII leakage
- Provide configuration options to exclude sensitive event types
- Follow existing Sentry patterns for `send_default_pii` configuration
- Ensure SQL queries and request bodies can be sanitized

### Performance Considerations
- Use efficient event pattern matching to minimize overhead
- Implement sampling to handle high-volume events
- Consider async processing for non-critical events
- Reuse existing event filtering and cleanup utilities from Rails integration
